<template>
  <a-card title="生产报工">
    <!-- 搜索区域 -->
    <a-form layout="inline" :model="searchForm">
      <a-form-item label="工单编号">
        <a-input
          v-model:value="searchForm.taskNo"
          placeholder="请输入工单编号"
          allow-clear
        ></a-input>
      </a-form-item>
      <a-form-item>
        <a-space>
          <a-button type="primary" @click="handleSearch">查询</a-button>
          <a-button @click="resetSearch">重置</a-button>
        </a-space>
      </a-form-item>
    </a-form>
    <a-divider></a-divider>

    <!-- 表格区域 -->
    <manage-base-table :columns="columns" :query="queryFn" ref="tableRef">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'status'">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusText(record.status) }}
          </a-tag>
        </template>
        <template v-if="column.key === 'progress'">
          <a-progress
            :percent="getProductionProgress(record)"
            size="small"
            :status="getProgressStatus(record)"
          ></a-progress>
        </template>
        <template v-if="column.key === 'action'">
          <a-button
            type="primary"
            @click="handleReporting(record)"
            :disabled="record.status !== 2"
          >
            报工
          </a-button>
        </template>
      </template>
    </manage-base-table>

    <!-- 报工模态框 -->
    <a-modal
      v-model:open="reportModalOpen"
      title="生产报工"
      @ok="handleReportSubmit"
      :confirmLoading="reportModalLoading"
    >
      <a-form :model="reportForm" layout="vertical">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="工单编号">
              <a-input v-model:value="reportForm.taskNo" disabled></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="产品名称">
              <a-input
                v-model:value="reportForm.productName"
                disabled
              ></a-input>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="计划数量">
              <a-input
                v-model:value="reportForm.planQuantity"
                disabled
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="已完成数量">
              <a-input
                v-model:value="reportForm.completedQuantity"
                disabled
              ></a-input>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="合格品数量" name="quantity">
              <a-input-number
                v-model:value="reportForm.quantity"
                :min="0"
                style="width: 100%"
                placeholder="请输入合格品数量"
                type="number"
              ></a-input-number>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="不合格品数量" name="unqualifiedQuantity">
              <a-input-number
                v-model:value="reportForm.unqualifiedQuantity"
                :min="0"
                style="width: 100%"
                placeholder="请输入不合格品数量(可选)"
                type="number"
              ></a-input-number>
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item label="备注" name="remark">
          <a-textarea
            v-model:value="reportForm.remark"
            placeholder="请输入备注信息(可选)"
            :rows="4"
          ></a-textarea>
        </a-form-item>
      </a-form>
    </a-modal>
  </a-card>
</template>

<script lang="ts" setup>
  import { message } from "ant-design-vue";
  import dayjs from "dayjs";

  // 表格列定义
  const columns = [
    { title: "工单编号", dataIndex: "code", key: "code" },
    {
      title: "产品名称",
      key: "productName",
      customRender: ({ record }: { record: any }) => {
        return record.Materiel?.name || "-";
      },
    },
    {
      title: "计划数量",
      dataIndex: "quantity",
      key: "quantity",
      customRender: ({ text }: { text: any }) => {
        return Number(text) || 0;
      },
    },
    {
      title: "已完成数量",
      dataIndex: "completed_quantity",
      key: "completed_quantity",
      customRender: ({ text }: { text: any }) => {
        return Number(text) || 0;
      },
    },
    {
      title: "计划开始时间",
      dataIndex: "startAt",
      key: "startAt",
      customRender: ({ text }: { text: any }) => {
        return text ? dayjs(text).format("YYYY-MM-DD") : "-";
      },
    },
    {
      title: "计划结束时间",
      dataIndex: "endAt",
      key: "endAt",
      customRender: ({ text }: { text: any }) => {
        return text ? dayjs(text).format("YYYY-MM-DD") : "-";
      },
    },
    { title: "状态", dataIndex: "status", key: "status" },
    { title: "进度", key: "progress" },
    { title: "操作", key: "action" },
  ];

  // 搜索表单
  const searchForm = reactive({
    taskNo: "",
  });

  // 报工表单
  const reportForm = reactive({
    id: null as number | null,
    taskNo: "",
    productName: "",
    planQuantity: 0,
    completedQuantity: 0,
    quantity: 1,
    unqualifiedQuantity: 0,
    remark: "",
  });

  // 模态框状态
  const reportModalOpen = ref(false);
  const reportModalLoading = ref(false);
  const tableRef = ref();

  // 函数: 获取当前用户被分配的工单
  const queryFn = async (params: any) => {
    try {
      // 获取当前用户信息
      const user = await useApiTrpc().public.auth.status.query();

      // 构建查询参数, 只查询分配给当前用户的工单
      const queryParams = {
        taskNo: searchForm.taskNo,
        includeUsers: true,
        ...params,
      };

      // 查询工单数据
      const response =
        await useApiTrpc().admin.production.queryProductionTask.query(
          queryParams
        );

      // 过滤只显示分配给当前用户的工单
      if (response && response.data && response.data.result) {
        const filteredTasks = response.data.result.filter((task: any) => {
          return (
            task.users &&
            task.users.some((u: any) => u.user_id === user.data?.id)
          );
        });

        return {
          data: {
            result: filteredTasks,
            total: filteredTasks.length,
          },
        };
      }

      return { data: { result: [], total: 0 } };
    } catch (error) {
      console.error("查询工单失败:", error);
      message.error("查询工单失败");
      return { data: { result: [], total: 0 } };
    }
  };

  // 搜索和重置函数
  const handleSearch = () => {
    tableRef.value?.query();
  };

  const resetSearch = () => {
    searchForm.taskNo = "";
    handleSearch();
  };

  // 状态展示函数
  const getStatusColor = (status: number) => {
    switch (status) {
      case 0:
        return "blue"; // 草稿
      case 1:
        return "orange"; // 已下达
      case 2:
        return "green"; // 生产中
      case 3:
        return "cyan"; // 已完成
      case 4:
        return "red"; // 已终止
      default:
        return "default";
    }
  };

  const getStatusText = (status: number) => {
    switch (status) {
      case 0:
        return "草稿";
      case 1:
        return "待领料";
      case 2:
        return "生产中";
      case 3:
        return "已完成";
      case 4:
        return "已终止";
      default:
        return "未知状态";
    }
  };

  // 计算生产进度
  const getProductionProgress = (record: any) => {
    if (!record.completed_quantity || !record.quantity) return 0;
    const completed = Number(record.completed_quantity);
    const total = Number(record.quantity);
    if (total === 0) return 0;

    const progress = Math.floor((completed / total) * 100);
    return progress > 100 ? 100 : progress;
  };

  const getProgressStatus = (record: any) => {
    if (record.status === 4) return "exception"; // 已终止
    if (record.status === 3) return "success"; // 已完成
    return "active"; // 其他状态
  };

  // 处理报工
  const handleReporting = (record: any) => {
    reportForm.id = record.id;
    reportForm.taskNo = record.code;
    reportForm.productName = record.Materiel?.name || "";
    reportForm.planQuantity = Number(record.quantity) || 0;
    reportForm.completedQuantity = Number(record.completed_quantity) || 0;
    reportForm.quantity = 1;
    reportForm.unqualifiedQuantity = 0;
    reportForm.remark = "";

    reportModalOpen.value = true;
  };

  // 提交报工
  const handleReportSubmit = async () => {
    if (
      reportForm.quantity == undefined ||
      reportForm.quantity == null ||
      reportForm.quantity < 0
    ) {
      message.error("请输入有效的合格品数量");
      return;
    }

    try {
      reportModalLoading.value = true;

      // 计算新的完成数量（合格品+不合格品）
      const totalQuantity =
        reportForm.quantity + (reportForm.unqualifiedQuantity || 0);
      const newCompletedQuantity = reportForm.completedQuantity + totalQuantity;

      // 调用API更新完成数量
      await useApiTrpc().admin.production.updateProductionTaskQuantity.mutate({
        id: reportForm.id as number,
        completed_quantity: newCompletedQuantity,
        unqualified_quantity: reportForm.unqualifiedQuantity || 0,
      });

      message.success("报工成功");
      reportModalOpen.value = false;

      // 刷新表格数据
      tableRef.value?.query();
    } catch (error) {
      console.error("报工失败:", error);
      message.error("报工失败");
    } finally {
      reportModalLoading.value = false;
    }
  };
</script>
